import React, { useState } from 'react';
import {
  ChevronDownIcon,
  ChevronUpIcon,
  QuestionMarkCircleIcon,
  ChatBubbleLeftRightIcon
} from '@heroicons/react/24/outline';

const FAQ: React.FC = () => {
  const [openIndex, setOpenIndex] = useState<number | null>(0);

  const faqs = [
    {
      question: "Combien coûte Sharyou ?",
      answer: "<PERSON>haryou propose un plan gratuit pour commencer, puis des plans premium à partir de 2 500 DA/mois. Nous avons la commission la plus basse du marché (1,5%) et aucun frais caché. Vous ne payez que quand vous vendez !"
    },
    {
      question: "Les outils IA sont-ils inclus dans tous les plans ?",
      answer: "Oui ! Tous nos outils d'intelligence artificielle (prédictions de ventes, tarification dynamique, ciblage client) sont inclus dans tous nos plans, même le plan gratuit. Nous croyons que l'IA doit être accessible à tous les entrepreneurs."
    },
    {
      question: "Quels moyens de paiement acceptez-vous ?",
      answer: "Nous supportons tous les moyens de paiement populaires en Algérie : CIB, EDAHABIA, Baridi Mob, paiement à la livraison, et les cartes internationales. L'intégration se fait en quelques clics."
    },
    {
      question: "Combien de temps faut-il pour créer ma boutique ?",
      answer: "Avec Sharyou, vous pouvez créer votre boutique complète en moins de 20 minutes ! Notre processus en 3 étapes simplifie tout : inscription (2 min), ajout des produits (10 min), personnalisation et lancement (5 min)."
    },
    {
      question: "Le support est-il disponible en français et arabe ?",
      answer: "Absolument ! Notre équipe support est disponible 24/7 en français et en arabe. Nous proposons aussi des formations gratuites et un accompagnement personnalisé pour vous aider à réussir."
    },
    {
      question: "Puis-je vendre à l'international ?",
      answer: "Oui ! Sharyou vous permet de vendre dans toute l'Algérie et à l'international. Nous gérons les devises multiples, les taxes internationales et nous avons des partenariats avec les meilleurs transporteurs."
    },
    {
      question: "Comment fonctionne la livraison ?",
      answer: "Nous avons un réseau de livraison couvrant les 48 wilayas avec suivi en temps réel. Tarifs négociés avec les meilleurs transporteurs, livraison express disponible, et gestion automatique des retours."
    },
    {
      question: "Mes données sont-elles sécurisées ?",
      answer: "La sécurité est notre priorité absolue. Nous utilisons un cryptage SSL 256-bit, des serveurs sécurisés en Europe, et nous sommes conformes aux normes internationales de protection des données. Vos informations sont 100% protégées."
    }
  ];

  return (
    <section id="faq" className="py-20 bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-20"></div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <QuestionMarkCircleIcon className="w-4 h-4" />
            Questions Fréquentes
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Tout ce que vous devez savoir
          </h2>
          <p className="text-xl text-gray-600 leading-relaxed">
            Trouvez rapidement les réponses à vos questions les plus courantes sur Sharyou
          </p>
        </div>

        {/* FAQ Items */}
        <div className="space-y-4 mb-16">
          {faqs.map((faq, index) => (
            <div key={index} className="bg-white rounded-2xl shadow-lg border border-gray-100 overflow-hidden hover:shadow-xl transition-all duration-300">
              <button
                className="w-full px-8 py-6 text-left flex justify-between items-center hover:bg-gray-50 transition-colors group"
                onClick={() => setOpenIndex(openIndex === index ? null : index)}
              >
                <span className="font-semibold text-gray-900 text-lg pr-4 group-hover:text-blue-600 transition-colors">
                  {faq.question}
                </span>
                <div className={`w-8 h-8 rounded-full flex items-center justify-center transition-all duration-300 ${
                  openIndex === index
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-100 text-gray-400 group-hover:bg-blue-100 group-hover:text-blue-600'
                }`}>
                  {openIndex === index ? (
                    <ChevronUpIcon className="w-5 h-5" />
                  ) : (
                    <ChevronDownIcon className="w-5 h-5" />
                  )}
                </div>
              </button>

              {openIndex === index && (
                <div className="px-8 pb-6 border-t border-gray-100 bg-gray-50">
                  <div className="pt-6">
                    <p className="text-gray-700 leading-relaxed text-lg">
                      {faq.answer}
                    </p>
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Contact CTA */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
            <ChatBubbleLeftRightIcon className="w-12 h-12 mx-auto mb-4 text-blue-200" />
            <h3 className="text-2xl font-bold mb-4">
              Vous avez d'autres questions ?
            </h3>
            <p className="text-blue-100 mb-6 text-lg">
              Notre équipe d'experts est disponible 24/7 pour vous aider à réussir
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
                Contacter le support
              </button>
              <button className="border-2 border-white text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:text-blue-600 transition-all duration-300">
                Planifier un appel
              </button>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default FAQ;