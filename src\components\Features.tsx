import React from 'react';
import {
  SparklesIcon,
  CreditCardIcon,
  PaintBrushIcon,
  TruckIcon,
  ChartBarIcon,
  ChatBubbleLeftRightIcon,
  CheckIcon,
  ArrowRightIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const Features: React.FC = () => {
  const mainFeatures = [
    {
      icon: SparklesIcon,
      title: "Outils IA Intégrés",
      description: "Optimisez vos ventes avec notre intelligence artificielle avancée qui analyse les tendances et recommande les meilleures stratégies pour votre boutique.",
      benefit: "Augmentation des ventes de +40%",
      color: "from-purple-600 to-pink-600",
      featured: true
    },
    {
      icon: CreditCardIcon,
      title: "Paiements Sécurisés",
      description: "Acceptez tous les moyens de paiement populaires en Algérie : CIB, EDAHABIA, Baridi Mob, et paiement à la livraison avec des frais ultra-compétitifs.",
      benefit: "Commission la plus basse : 1.5%",
      color: "from-green-600 to-emerald-600",
      featured: true
    },
    {
      icon: PaintBrushIcon,
      title: "Créateur Visuel",
      description: "Concevez votre boutique en ligne avec notre éditeur drag & drop intuitif. Plus de 100 templates professionnels adaptés au marché algérien.",
      benefit: "Boutique prête en 5 minutes",
      color: "from-blue-600 to-indigo-600",
      featured: false
    },
    {
      icon: TruckIcon,
      title: "Livraison Nationale",
      description: "Réseau de livraison couvrant les 48 wilayas avec suivi en temps réel et tarifs négociés avec les meilleurs transporteurs d'Algérie.",
      benefit: "Livraison dans toute l'Algérie",
      color: "from-orange-600 to-red-600",
      featured: false
    },
    {
      icon: ChartBarIcon,
      title: "Analytics Avancés",
      description: "Tableaux de bord détaillés avec insights sur vos clients, produits populaires, et recommandations personnalisées pour optimiser vos performances.",
      benefit: "Décisions basées sur les données",
      color: "from-indigo-600 to-purple-600",
      featured: false
    },
    {
      icon: ChatBubbleLeftRightIcon,
      title: "Support 24/7",
      description: "Équipe support dédiée disponible en français et arabe, avec chat en direct, formation gratuite et accompagnement personnalisé.",
      benefit: "Assistance en français et arabe",
      color: "from-teal-600 to-cyan-600",
      featured: false
    }
  ];

  return (
    <section id="fonctionnalites" className="py-20 bg-white relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-20"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <StarIcon className="w-4 h-4" />
            Fonctionnalités Premium
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Tout ce dont vous avez besoin pour réussir
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Une suite complète d'outils professionnels conçus spécialement pour les entrepreneurs algériens.
            De la création à la gestion, nous vous accompagnons à chaque étape de votre succès.
          </p>
        </div>

        {/* Featured Features Grid */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {mainFeatures.filter(feature => feature.featured).map((feature, index) => (
            <div
              key={index}
              className="group relative bg-gradient-to-br from-white to-gray-50 rounded-3xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 overflow-hidden"
            >
              {/* Background Gradient */}
              <div className={`absolute top-0 right-0 w-32 h-32 bg-gradient-to-br ${feature.color} opacity-10 rounded-full -translate-y-16 translate-x-16 group-hover:scale-150 transition-transform duration-500`}></div>

              <div className="relative">
                <div className={`w-16 h-16 bg-gradient-to-r ${feature.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                  <feature.icon className="w-8 h-8 text-white" />
                </div>

                <h3 className="text-2xl font-bold text-gray-900 mb-4">
                  {feature.title}
                </h3>

                <p className="text-gray-600 leading-relaxed mb-6 text-lg">
                  {feature.description}
                </p>

                <div className="flex items-center gap-2 text-green-600 font-semibold">
                  <CheckIcon className="w-5 h-5" />
                  <span>{feature.benefit}</span>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Regular Features Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
          {mainFeatures.filter(feature => !feature.featured).map((feature, index) => (
            <div
              key={index}
              className="group bg-white rounded-2xl p-6 shadow-md hover:shadow-xl transition-all duration-300 border border-gray-100 hover:border-gray-200"
            >
              <div className={`w-12 h-12 bg-gradient-to-r ${feature.color} rounded-xl flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300`}>
                <feature.icon className="w-6 h-6 text-white" />
              </div>

              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {feature.title}
              </h3>

              <p className="text-gray-600 leading-relaxed text-sm mb-4">
                {feature.description}
              </p>

              <div className="flex items-center gap-2 text-green-600 text-sm font-medium">
                <CheckIcon className="w-4 h-4" />
                <span>{feature.benefit}</span>
              </div>
            </div>
          ))}
        </div>

        {/* Call to Action */}
        <div className="text-center mt-16">
          <div className="bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-8 text-white">
            <h3 className="text-2xl font-bold mb-4">
              Prêt à transformer votre business ?
            </h3>
            <p className="text-blue-100 mb-6 text-lg">
              Rejoignez plus de 50 000 commerçants qui ont choisi Sharyou pour développer leur présence en ligne
            </p>
            <button className="bg-white text-blue-600 px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1">
              Commencer gratuitement
              <ArrowRightIcon className="w-5 h-5 inline-block ml-2" />
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Features;