import React from 'react';
import {
  SparklesIcon,
  ChartBarIcon,
  UserGroupIcon,
  LightBulbIcon,
  BoltIcon,
  ArrowTrendingUpIcon,
  CpuChipIcon,
  RocketLaunchIcon
} from '@heroicons/react/24/outline';

const AITools: React.FC = () => {
  const tools = [
    {
      icon: CpuChipIcon,
      title: "Prédictions de Ventes IA",
      description: "Notre intelligence artificielle analyse vos données historiques et les tendances du marché pour prédire avec précision vos futures ventes et optimiser votre stock.",
      benefits: [
        "Prédictions précises à 94%",
        "Optimisation automatique du stock",
        "Réduction des ruptures de stock"
      ],
      color: 'from-blue-600 to-indigo-600',
      impact: '+35% Revenus'
    },
    {
      icon: ChartBarIcon,
      title: "Tarification Dynamique",
      description: "Ajustez automatiquement vos prix en fonction de la demande, de la concurrence et des tendances saisonnières pour maximiser vos profits.",
      benefits: [
        "Ajustement automatique des prix",
        "Analyse de la concurrence en temps réel",
        "Optimisation des marges"
      ],
      color: 'from-green-600 to-emerald-600',
      impact: '+25% Profits'
    },
    {
      icon: UserGroupIcon,
      title: "Ciblage Client Intelligent",
      description: "Identifiez et ciblez automatiquement vos clients les plus rentables avec des campagnes marketing personnalisées générées par IA.",
      benefits: [
        "Segmentation automatique des clients",
        "Campagnes marketing personnalisées",
        "Recommandations produits intelligentes"
      ],
      color: 'from-purple-600 to-pink-600',
      impact: '+40% Conversion'
    }
  ];

  const stats = [
    { value: "94%", label: "Précision des prédictions" },
    { value: "+35%", label: "Augmentation des ventes" },
    { value: "24/7", label: "Optimisation continue" },
    { value: "15min", label: "Configuration rapide" }
  ];

  return (
    <section id="outils-ia" className="py-20 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 relative overflow-hidden">
      {/* Background Elements */}
      <div className="absolute inset-0 bg-grid-white/10 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-50"></div>
      <div className="absolute top-1/4 left-1/4 w-96 h-96 bg-purple-500/20 rounded-full blur-3xl"></div>
      <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-blue-500/20 rounded-full blur-3xl"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center px-4 py-2 bg-purple-500/20 backdrop-blur-sm border border-purple-300/30 rounded-full text-purple-300 text-sm font-medium mb-6">
            <SparklesIcon className="w-4 h-4 mr-2" />
            Intelligence Artificielle
          </div>

          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-6">
            Boostez vos ventes avec l'IA
          </h2>
          <p className="text-xl text-gray-300 max-w-4xl mx-auto leading-relaxed">
            Découvrez notre suite d'outils IA révolutionnaires qui automatisent et optimisent chaque aspect de votre business.
            Prédictions de ventes, tarification intelligente et ciblage client personnalisé.
          </p>
        </div>

        {/* AI Tools Grid */}
        <div className="grid lg:grid-cols-3 gap-8 mb-16">
          {tools.map((tool, index) => (
            <div key={index} className="group bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20 hover:bg-white/20 transition-all duration-500 hover:scale-105">
              {/* Impact Badge */}
              <div className="flex items-center justify-between mb-6">
                <div className={`w-16 h-16 bg-gradient-to-r ${tool.color} rounded-2xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-xl`}>
                  <tool.icon className="w-8 h-8 text-white" />
                </div>
                <div className="bg-green-500/20 text-green-300 px-3 py-1 rounded-full text-sm font-bold">
                  {tool.impact}
                </div>
              </div>

              <h3 className="text-xl font-bold text-white mb-4">
                {tool.title}
              </h3>

              <p className="text-gray-300 mb-6 leading-relaxed">
                {tool.description}
              </p>

              <div className="space-y-3">
                {tool.benefits.map((benefit, idx) => (
                  <div key={idx} className="flex items-start gap-3">
                    <BoltIcon className="w-5 h-5 text-yellow-400 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-300 text-sm">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>

        {/* Stats */}
        <div className="bg-white/10 backdrop-blur-sm rounded-3xl p-8 border border-white/20">
          <h3 className="text-2xl font-bold text-white text-center mb-8">
            Résultats prouvés par nos clients
          </h3>

          <div className="grid grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="text-3xl md:text-4xl font-bold bg-gradient-to-r from-purple-400 to-pink-400 bg-clip-text text-transparent mb-2">
                  {stat.value}
                </div>
                <p className="text-gray-300 text-sm">{stat.label}</p>
              </div>
            ))}
          </div>
        </div>

        {/* CTA */}
        <div className="text-center mt-16">
          <div className="max-w-2xl mx-auto">
            <h3 className="text-2xl font-bold text-white mb-4">
              Prêt à booster votre business avec l'IA ?
            </h3>
            <p className="text-gray-300 mb-8">
              Rejoignez des milliers de commerçants qui utilisent l'IA pour automatiser leurs opérations et augmenter leurs ventes
            </p>
            <button className="group bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl font-semibold text-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-1">
              Activer l'IA maintenant
              <RocketLaunchIcon className="w-5 h-5 inline-block ml-2 group-hover:translate-x-1 transition-transform" />
            </button>
            <p className="text-gray-400 text-sm mt-4">
              Essai gratuit de 30 jours • Aucune carte de crédit requise
            </p>
          </div>
        </div>
      </div>
    </section>
  );
};

export default AITools;