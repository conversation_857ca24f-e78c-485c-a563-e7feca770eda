import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  UsersIcon,
  CurrencyDollarIcon,
  GlobeAltIcon,
  BuildingStorefrontIcon,
  TruckIcon,
  ShieldCheckIcon,
  ClockIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';

const PlatformStats: React.FC = () => {
  const { t } = useTranslation();

  const mainStats = [
    {
      value: "50,000+",
      label: t('stats_active_merchants'),
      description: t('stats_active_merchants_desc'),
      icon: UsersIcon,
      color: "from-blue-600 to-indigo-600"
    },
    {
      value: "$2.5M+",
      label: t('stats_monthly_sales'),
      description: t('stats_monthly_sales_desc'),
      icon: CurrencyDollarIcon,
      color: "from-green-600 to-emerald-600"
    },
    {
      value: "48",
      label: t('stats_cities_covered'),
      description: t('stats_cities_covered_desc'),
      icon: GlobeAltIcon,
      color: "from-purple-600 to-violet-600"
    },
    {
      value: "99.9%",
      label: t('stats_uptime'),
      description: t('stats_uptime_desc'),
      icon: ShieldCheckIcon,
      color: "from-orange-600 to-red-600"
    }
  ];

  const additionalStats = [
    {
      value: "500K+",
      label: t('stats_orders_processed'),
      icon: BuildingStorefrontIcon
    },
    {
      value: "24/7",
      label: t('stats_support_available'),
      icon: ClockIcon
    },
    {
      value: "300%",
      label: t('stats_avg_growth'),
      icon: ChartBarIcon
    },
    {
      value: "48h",
      label: t('stats_setup_time'),
      icon: TruckIcon
    }
  ];

  return (
    <section className="py-20 bg-gradient-to-br from-slate-50 to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-30"></div>
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <ChartBarIcon className="w-4 h-4" />
            {t('stats_section_badge')}
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            {t('stats_section_title')}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t('stats_section_subtitle')}
          </p>
        </div>

        {/* Main Statistics Grid */}
        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
          {mainStats.map((stat, index) => (
            <div 
              key={index}
              className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100"
            >
              <div className={`w-16 h-16 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}>
                <stat.icon className="w-8 h-8 text-white" />
              </div>
              
              <div className="text-4xl font-bold text-gray-900 mb-2">
                {stat.value}
              </div>
              
              <h3 className="text-lg font-semibold text-gray-900 mb-3">
                {stat.label}
              </h3>
              
              <p className="text-gray-600 text-sm leading-relaxed">
                {stat.description}
              </p>
            </div>
          ))}
        </div>

        {/* Additional Stats Bar */}
        <div className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
            {additionalStats.map((stat, index) => (
              <div key={index} className="text-center">
                <div className="w-12 h-12 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-xl flex items-center justify-center mx-auto mb-4">
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
                <div className="text-2xl font-bold text-gray-900 mb-1">
                  {stat.value}
                </div>
                <div className="text-sm text-gray-600 font-medium">
                  {stat.label}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Trust Indicators */}
        <div className="mt-16 text-center">
          <p className="text-gray-600 mb-8 text-lg">
            {t('stats_trusted_by')}
          </p>
          <div className="flex flex-wrap items-center justify-center gap-8 opacity-60">
            {/* Placeholder for partner/customer logos */}
            <div className="bg-gray-200 rounded-lg px-6 py-3 text-gray-500 font-medium">
              Algeria Post
            </div>
            <div className="bg-gray-200 rounded-lg px-6 py-3 text-gray-500 font-medium">
              CIB Bank
            </div>
            <div className="bg-gray-200 rounded-lg px-6 py-3 text-gray-500 font-medium">
              BaridiMob
            </div>
            <div className="bg-gray-200 rounded-lg px-6 py-3 text-gray-500 font-medium">
              Yassir Express
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default PlatformStats;
