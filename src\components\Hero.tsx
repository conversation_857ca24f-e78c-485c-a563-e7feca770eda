import React from 'react';
import {
  ArrowRightIcon,
  PlayIcon,
  StarIcon
} from '@heroicons/react/24/outline';

const Hero: React.FC = () => {
  return (
    <section className="h-screen flex items-center justify-center relative overflow-hidden">
      {/* Video Background with 2% Blur */}
      <video
        autoPlay
        loop
        muted
        playsInline
        className="absolute inset-0 w-full h-full object-cover"
        style={{ filter: 'blur(2px)' }}
      >
        <source src="/video.mp4" type="video/mp4" />
      </video>

      {/* Overlay */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-900/30 via-white/70 to-indigo-900/30"></div>

      {/* Content */}
      <div className="w-full max-w-5xl mx-auto px-6 sm:px-8 lg:px-12 xl:px-16 relative z-10">
        <div className="flex flex-col items-center text-center space-y-6">
          {/* Badge */}
          <div className="inline-flex items-center justify-center px-4 py-2 bg-blue-100/90 backdrop-blur-sm rounded-full text-blue-700 text-sm font-medium tracking-wide">
            <StarIcon className="w-4 h-4 mr-2 flex-shrink-0" />
            <span>Plateforme N°1 en Algérie</span>
          </div>

          {/* Main Heading */}
          <div className="w-full max-w-4xl mx-auto">
            <h1 className="text-3xl sm:text-4xl md:text-5xl font-bold text-gray-900 leading-tight tracking-tight">
              Avec <span className="bg-gradient-to-r from-blue-600 to-indigo-600 bg-clip-text text-transparent">Sharyou</span> créez votre boutique rapidement
            </h1>
          </div>

          {/* Description */}
          <div className="w-full max-w-3xl mx-auto">
            <p className="text-base sm:text-lg text-gray-700 leading-relaxed">
              La plateforme e-commerce simple et puissante pour les entrepreneurs algériens.
            </p>
          </div>

          {/* Buttons */}
          <div className="flex flex-row items-center justify-center gap-4 w-full max-w-4xl mx-auto">
            <button className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-6 py-3 rounded-xl font-semibold hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1 flex items-center justify-center group backdrop-blur-sm whitespace-nowrap">
              <span>Créer ma boutique gratuitement</span>
              <ArrowRightIcon className="w-4 h-4 ml-2 flex-shrink-0 group-hover:translate-x-1 transition-transform" />
            </button>

            <button className="flex items-center justify-center px-6 py-3 border-2 border-gray-300 bg-white/90 backdrop-blur-sm rounded-xl text-gray-700 font-semibold hover:bg-gray-50 hover:border-gray-400 transition-all duration-300 whitespace-nowrap">
              <PlayIcon className="w-4 h-4 mr-2 flex-shrink-0" />
              <span>Voir la démonstration</span>
            </button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Hero;