import React from 'react';
import {
  StarIcon,
  ArrowTrendingUpIcon,
  CalendarIcon,
  SparklesIcon,
  CheckBadgeIcon
} from '@heroicons/react/24/outline';

const Testimonials: React.FC = () => {
  const testimonials = [
    {
      name: "<PERSON><PERSON>",
      business: "Boutique Mode Féminine",
      location: "Alger, Algérie",
      revenue: "+150K DA/mois",
      image: "https://images.pexels.com/photos/3769021/pexels-photo-3769021.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      quote: "Sharyou a transformé mon petit business local en une boutique en ligne prospère. En 6 mois, j'ai multiplié mes ventes par 4 !",
      before: "15K DA/mois",
      after: "150K DA/mois",
      timeframe: "6 mois",
      keyFeature: "Outils IA Marketing",
      rating: 5,
      featured: true
    },
    {
      name: "<PERSON><PERSON>",
      business: "Électronique & Tech",
      location: "Oran, Algérie",
      revenue: "+300K DA/mois",
      image: "https://images.pexels.com/photos/2379004/pexels-photo-2379004.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      quote: "L'interface est si simple que j'ai pu créer ma boutique en une soirée. Le support client est exceptionnel, toujours disponible en français.",
      before: "50K DA/mois",
      after: "300K DA/mois",
      timeframe: "4 mois",
      keyFeature: "Support 24/7",
      rating: 5,
      featured: true
    },
    {
      name: "Fatima Zerrouki",
      business: "Artisanat Traditionnel",
      location: "Constantine, Algérie",
      revenue: "+80K DA/mois",
      image: "https://images.pexels.com/photos/3823495/pexels-photo-3823495.jpeg?auto=compress&cs=tinysrgb&w=150&h=150&fit=crop",
      quote: "Grâce à Sharyou, j'ai pu faire connaître mon artisanat traditionnel au-delà de ma région. Les outils de marketing sont fantastiques !",
      before: "20K DA/mois",
      after: "80K DA/mois",
      timeframe: "8 mois",
      keyFeature: "Marketing Automatisé",
      rating: 5,
      featured: false
    }
  ];

  return (
    <section id="temoignages" className="py-20 bg-gradient-to-br from-gray-50 to-blue-50 relative overflow-hidden">
      {/* Background Pattern */}
      <div className="absolute inset-0 bg-grid-slate-100 [mask-image:linear-gradient(0deg,transparent,black,transparent)] opacity-30"></div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Section Header */}
        <div className="text-center mb-16">
          <div className="inline-flex items-center gap-2 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium mb-6">
            <CheckBadgeIcon className="w-4 h-4" />
            Témoignages Clients
          </div>
          <h2 className="text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-6">
            Ils ont réussi avec Sharyou
          </h2>
          <p className="text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
            Découvrez comment nos clients ont transformé leur business et multiplié leurs ventes grâce à notre plateforme.
          </p>
        </div>

        {/* Featured Success Stories */}
        <div className="grid lg:grid-cols-2 gap-8 mb-16">
          {testimonials.filter(testimonial => testimonial.featured).map((testimonial, index) => (
            <div key={index} className="bg-white rounded-3xl p-8 shadow-xl border border-gray-100 relative overflow-hidden group hover:shadow-2xl transition-all duration-500">
              {/* Background Gradient */}
              <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-blue-600 to-indigo-600 opacity-10 rounded-full -translate-y-16 translate-x-16 group-hover:scale-150 transition-transform duration-500"></div>

              <div className="relative">
                {/* Quote Icon */}
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                  <span className="text-2xl text-blue-600 font-bold">"</span>
                </div>

                {/* Testimonial Quote */}
                <p className="text-gray-700 mb-8 leading-relaxed text-lg italic">
                  "{testimonial.quote}"
                </p>

                {/* Results Section */}
                <div className="bg-gradient-to-r from-green-50 to-blue-50 rounded-2xl p-6 mb-6">
                  <h4 className="font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <ArrowTrendingUpIcon className="w-5 h-5 text-green-600" />
                    Results in {testimonial.timeframe}
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm text-gray-600 mb-1">Before</div>
                      <div className="text-lg font-bold text-gray-900">{testimonial.before}</div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600 mb-1">After</div>
                      <div className="text-lg font-bold text-green-600">{testimonial.after}</div>
                    </div>
                  </div>
                </div>

                {/* Key Feature */}
                <div className="flex items-center gap-2 bg-blue-100 text-blue-700 px-4 py-2 rounded-full w-fit mb-6">
                  <SparklesIcon className="w-4 h-4" />
                  <span className="text-sm font-medium">Key Feature: {testimonial.keyFeature}</span>
                </div>

                {/* Rating */}
                <div className="flex items-center mb-6">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <StarIcon key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                  ))}
                  <span className="ml-2 text-gray-600 text-sm">5.0 rating</span>
                </div>

                {/* Profile */}
                <div className="flex items-center">
                  <img
                    src={testimonial.image}
                    alt={testimonial.name}
                    className="w-14 h-14 rounded-full object-cover mr-4 border-2 border-white shadow-lg"
                  />
                  <div>
                    <h4 className="font-bold text-gray-900 text-lg">{testimonial.name}</h4>
                    <p className="text-gray-600">{testimonial.business}</p>
                    <p className="text-gray-500 text-sm">{testimonial.location}</p>
                  </div>
                </div>

                {/* Revenue Badge */}
                <div className="absolute top-6 right-6 bg-gradient-to-r from-green-600 to-emerald-600 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg">
                  {testimonial.revenue}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Additional Testimonial */}
        <div className="max-w-2xl mx-auto">
          {testimonials.filter(testimonial => !testimonial.featured).map((testimonial, index) => (
            <div key={index} className="bg-white rounded-2xl p-8 shadow-lg border border-gray-100 text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-2xl text-blue-600 font-bold">"</span>
              </div>

              <p className="text-gray-700 mb-6 leading-relaxed text-lg italic">
                "{testimonial.quote}"
              </p>

              <div className="flex items-center justify-center mb-4">
                {[...Array(testimonial.rating)].map((_, i) => (
                  <StarIcon key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                ))}
              </div>

              <div className="flex items-center justify-center">
                <img
                  src={testimonial.image}
                  alt={testimonial.name}
                  className="w-12 h-12 rounded-full object-cover mr-4"
                />
                <div className="text-left">
                  <h4 className="font-semibold text-gray-900">{testimonial.name}</h4>
                  <p className="text-gray-600 text-sm">{testimonial.business}</p>
                </div>
              </div>

              <div className="mt-4 bg-green-100 text-green-700 px-4 py-2 rounded-full text-sm font-medium w-fit mx-auto">
                {testimonial.revenue}
              </div>
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-12 sm:mt-16 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-2xl p-6 sm:p-8 text-white">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 text-center">
            <div>
              <div className="text-2xl sm:text-3xl font-bold mb-2">50,000+</div>
              <p className="text-blue-100 text-xs sm:text-sm">Vendeurs Satisfaits</p>
            </div>
            <div>
              <div className="text-2xl sm:text-3xl font-bold mb-2">4.9/5</div>
              <p className="text-blue-100 text-xs sm:text-sm">Note Moyenne</p>
            </div>
            <div>
              <div className="text-2xl sm:text-3xl font-bold mb-2">+250%</div>
              <p className="text-blue-100 text-xs sm:text-sm">Croissance Moyenne</p>
            </div>
            <div>
              <div className="text-2xl sm:text-3xl font-bold mb-2">24/7</div>
              <p className="text-blue-100 text-xs sm:text-sm">Support Disponible</p>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default Testimonials;